use sqlx::PgPool;

#[tokio::main]
async fn main() -> Result<(), sqlx::Error> {
    println!("🚀 Starting migration process...");

    let database_url = std::env::var("DATABASE_URL")
        .expect("DATABASE_URL environment variable not set");
    println!("📊 Database URL: {}", database_url);

    println!("🔌 Connecting to database...");
    let pool = PgPool::connect(&database_url).await?;
    println!("✅ Connected to database successfully");

    println!("📁 Running migrations from ./migrations directory...");
    sqlx::migrate!("./migrations").run(&pool).await?;
    println!("✅ Migrations applied successfully");
    Ok(())
}