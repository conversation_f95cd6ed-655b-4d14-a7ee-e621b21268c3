use anyhow::Result;
use shared::{
    TelegramMessage, TelegramUser, TelegramChannel,
    CreateUserRequest, CreateChannelRequest,
    BatchCreateMessagesRequest
};
use std::collections::{HashMap, VecDeque};
use tokio::time::{Duration, Instant};
use tracing::{error, info, warn, debug};

use crate::api_client::ApiClient;

pub struct MessageProcessor {
    api_client: ApiClient,
    message_buffer: VecDeque<TelegramMessage>,
    user_cache: HashMap<i64, TelegramUser>,
    channel_cache: HashMap<i64, TelegramChannel>,
    last_batch_send: Instant,
    batch_size: usize,
    batch_timeout: Duration,
}

impl MessageProcessor {
    pub fn new(api_client: ApiClient) -> Self {
        Self {
            api_client,
            message_buffer: VecDeque::new(),
            user_cache: HashMap::new(),
            channel_cache: HashMap::new(),
            last_batch_send: Instant::now(),
            batch_size: 50, // Send messages in batches of 50
            batch_timeout: Duration::from_secs(30), // Send batch every 30 seconds
        }
    }

    pub async fn process_message(&mut self, message: TelegramMessage) -> Result<()> {
        info!("🔄 Processing message ID: {} from channel: {} (type: {:?})",
               message.telegram_message_id,
               message.channel_id,
               message.message_type);

        debug!("Message details - Sender: {:?}, Content length: {}, Sent at: {:?}",
               message.sender_id,
               message.content.len(),
               message.sent_at);

        // Ensure channel exists in backend
        if !self.channel_cache.contains_key(&message.channel_id) {
            info!("🏢 Channel {} not in cache, ensuring it exists in backend", message.channel_id);
            if let Err(e) = self.ensure_channel_exists(message.channel_id).await {
                warn!("❌ Failed to ensure channel exists: {}", e);
            } else {
                info!("✅ Channel {} ensured in backend", message.channel_id);
            }
        } else {
            debug!("✅ Channel {} already in cache", message.channel_id);
        }

        // Ensure user exists in backend (if message has a sender)
        if let Some(sender_id) = message.sender_id {
            if !self.user_cache.contains_key(&sender_id) {
                info!("👤 User {} not in cache, ensuring it exists in backend", sender_id);
                if let Err(e) = self.ensure_user_exists(sender_id).await {
                    warn!("❌ Failed to ensure user exists: {}", e);
                } else {
                    info!("✅ User {} ensured in backend", sender_id);
                }
            } else {
                debug!("✅ User {} already in cache", sender_id);
            }
        } else {
            debug!("📝 Message has no sender (system message?)");
        }

        // Add message to buffer
        self.message_buffer.push_back(message);
        info!("📝 Message added to buffer (buffer size: {})", self.message_buffer.len());

        // Check if we should send a batch
        let should_send_batch = self.message_buffer.len() >= self.batch_size
            || self.last_batch_send.elapsed() >= self.batch_timeout;

        if should_send_batch {
            info!("📦 Triggering batch send - Buffer size: {}, Time since last batch: {:?}",
                  self.message_buffer.len(),
                  self.last_batch_send.elapsed());
            self.send_message_batch().await?;
        } else {
            debug!("⏳ Batch not ready - Buffer: {}/{}, Time elapsed: {:?}/{:?}",
                   self.message_buffer.len(),
                   self.batch_size,
                   self.last_batch_send.elapsed(),
                   self.batch_timeout);
        }

        Ok(())
    }

    async fn send_message_batch(&mut self) -> Result<()> {
        if self.message_buffer.is_empty() {
            debug!("📦 Batch send called but buffer is empty");
            return Ok(());
        }

        let messages: Vec<TelegramMessage> = self.message_buffer.drain(..).collect();
        let message_count = messages.len();

        info!("📤 Sending batch of {} messages to backend", message_count);

        // Log some details about the messages being sent
        for (i, msg) in messages.iter().enumerate().take(3) {
            debug!("  Message {}: ID={}, Channel={}, Type={:?}",
                   i + 1, msg.telegram_message_id, msg.channel_id, msg.message_type);
        }
        if messages.len() > 3 {
            debug!("  ... and {} more messages", messages.len() - 3);
        }

        let request = BatchCreateMessagesRequest { messages };

        match self.api_client.create_messages_batch(request).await {
            Ok(response) => {
                info!("✅ Successfully sent batch: processed={}, failed={}",
                      response.processed_count, response.failed_count);

                if response.failed_count > 0 {
                    warn!("⚠️ Some messages failed to process: {}", response.message);
                } else {
                    info!("🎉 All {} messages processed successfully!", response.processed_count);
                }
            }
            Err(e) => {
                error!("❌ Failed to send message batch: {}", e);
                // TODO: Implement retry logic or dead letter queue
                return Err(e);
            }
        }

        self.last_batch_send = Instant::now();
        info!("⏰ Batch send completed, timer reset");
        Ok(())
    }

    async fn ensure_channel_exists(&mut self, channel_id: i64) -> Result<()> {
        debug!("Ensuring channel {} exists in backend", channel_id);

        // Try to get channel info from Telegram
        // For now, create a minimal channel record
        let channel = TelegramChannel {
            id: channel_id,
            title: format!("Channel {}", channel_id),
            username: None,
            description: None,
            member_count: None,
            is_public: false,
            is_verified: false,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let request = CreateChannelRequest { channel: channel.clone() };

        match self.api_client.create_channel(request).await {
            Ok(_) => {
                info!("Successfully created/updated channel: {}", channel_id);
                self.channel_cache.insert(channel_id, channel);
            }
            Err(e) => {
                warn!("Failed to create/update channel {}: {}", channel_id, e);
                // Still cache it to avoid repeated attempts
                self.channel_cache.insert(channel_id, channel);
            }
        }

        Ok(())
    }

    async fn ensure_user_exists(&mut self, user_id: i64) -> Result<()> {
        debug!("Ensuring user {} exists in backend", user_id);

        // Try to get user info from Telegram
        // For now, create a minimal user record
        let user = TelegramUser {
            id: user_id,
            username: None,
            first_name: None,
            last_name: None,
            phone_number: None,
            is_bot: false,
            is_premium: false,
            language_code: None,
            last_seen: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        let request = CreateUserRequest { user: user.clone() };

        match self.api_client.create_user(request).await {
            Ok(_) => {
                info!("Successfully created/updated user: {}", user_id);
                self.user_cache.insert(user_id, user);
            }
            Err(e) => {
                warn!("Failed to create/update user {}: {}", user_id, e);
                // Still cache it to avoid repeated attempts
                self.user_cache.insert(user_id, user);
            }
        }

        Ok(())
    }

    pub async fn flush_remaining_messages(&mut self) -> Result<()> {
        if !self.message_buffer.is_empty() {
            info!("Flushing {} remaining messages", self.message_buffer.len());
            self.send_message_batch().await?;
        }
        Ok(())
    }

    pub fn get_stats(&self) -> ProcessorStats {
        ProcessorStats {
            buffered_messages: self.message_buffer.len(),
            cached_users: self.user_cache.len(),
            cached_channels: self.channel_cache.len(),
            last_batch_send: self.last_batch_send,
        }
    }
}

#[derive(Debug)]
pub struct ProcessorStats {
    pub buffered_messages: usize,
    pub cached_users: usize,
    pub cached_channels: usize,
    pub last_batch_send: Instant,
}
