#!/bin/bash

# Test script for telegram-ingester with enhanced debugging

echo "🚀 Starting Telegram Ingester with enhanced debugging..."
echo "📋 Configuration:"
echo "  - Log level: DEBUG"
echo "  - Config file: Config.local.toml"
echo "  - Monitoring supergroup: -1001327536928"
echo ""

# Set environment variables for maximum debugging
export RUST_LOG="telegram_ingester=debug,grammers_client=info"
export RUST_BACKTRACE=1
export CONFIG_PATH="Config.local.toml"

# Build the project first
echo "🔨 Building telegram-ingester..."
cargo build --bin telegram-ingester

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo "✅ Build successful!"
echo ""
echo "🔄 Starting ingester..."
echo "📝 Watch for these log messages:"
echo "  - '🔗 Attempting to join channel' - Channel joining"
echo "  - '🏢 Detected supergroup/channel ID' - Supergroup detection"
echo "  - '📨 Received X new messages' - Message reception"
echo "  - '🔄 Processing new message from chat' - Message processing"
echo "  - '✅ Successfully converted message' - Successful conversion"
echo ""
echo "Press Ctrl+C to stop"
echo "----------------------------------------"

# Run the ingester
cargo run --bin telegram-ingester
